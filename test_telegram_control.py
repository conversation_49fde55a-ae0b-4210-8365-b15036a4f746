#!/usr/bin/env python3
"""
Test script for Telegram Control Bot
اختبار بوت التحكم في التيليجرام
"""

import os
import sys
import json
from dotenv import load_dotenv

def test_environment():
    """Test environment configuration"""
    print("🧪 Testing Environment Configuration...")
    print("-" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check required variables
    required_vars = [
        'TELEGRAM_CONTROL_BOT_TOKEN',
        'TELEGRAM_ADMIN_USER_ID',
        'TELEGRAM_BOT_TOKEN',
        'BLOG_ID',
        'GEMINI_API_KEY_1'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value in ['YOUR_CONTROL_BOT_TOKEN_HERE', 'YOUR_USER_ID_HERE']:
            missing_vars.append(var)
            print(f"❌ {var}: Missing or not configured")
        else:
            # Hide sensitive data
            if 'TOKEN' in var or 'KEY' in var:
                display_value = value[:10] + "..." if len(value) > 10 else "***"
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
    
    if missing_vars:
        print(f"\n⚠️ Missing variables: {', '.join(missing_vars)}")
        return False
    else:
        print("\n✅ All required environment variables are configured!")
        return True

def test_imports():
    """Test if all required modules can be imported"""
    print("\n🧪 Testing Module Imports...")
    print("-" * 50)
    
    modules_to_test = [
        ('telegram', 'python-telegram-bot'),
        ('dotenv', 'python-dotenv'),
        ('requests', 'requests'),
        ('bs4', 'beautifulsoup4'),
        ('selenium', 'selenium'),
        ('google.auth', 'google-auth'),
        ('google.generativeai', 'google-generativeai'),
        ('PIL', 'Pillow')
    ]
    
    failed_imports = []
    for module_name, package_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}: OK")
        except ImportError:
            print(f"❌ {module_name}: Failed (install with: pip install {package_name})")
            failed_imports.append(package_name)
    
    if failed_imports:
        print(f"\n⚠️ Failed imports. Run: pip install {' '.join(failed_imports)}")
        return False
    else:
        print("\n✅ All modules imported successfully!")
        return True

def test_file_structure():
    """Test if all required files exist"""
    print("\n🧪 Testing File Structure...")
    print("-" * 50)
    
    required_files = [
        'main.py',
        'config.py',
        'telegram_control_bot.py',
        'run_telegram_bot.py',
        '.env',
        'requirements.txt'
    ]
    
    required_dirs = [
        'scraper',
        'generator', 
        'publisher',
        'utils',
        'database'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: Found")
        else:
            print(f"❌ {file_path}: Missing")
            missing_files.append(file_path)
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}/: Found")
        else:
            print(f"❌ {dir_path}/: Missing")
            missing_files.append(dir_path)
    
    if missing_files:
        print(f"\n⚠️ Missing files/directories: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ All required files and directories found!")
        return True

def test_bot_state():
    """Test bot state management"""
    print("\n🧪 Testing Bot State Management...")
    print("-" * 50)
    
    try:
        # Test creating bot state
        from telegram_control_bot import BotState
        
        bot_state = BotState()
        print("✅ BotState class: OK")
        
        # Test saving state
        bot_state.articles_processed = 5
        bot_state.save_state()
        print("✅ Save state: OK")
        
        # Test loading state
        new_bot_state = BotState()
        new_bot_state.load_state()
        
        if new_bot_state.articles_processed == 5:
            print("✅ Load state: OK")
        else:
            print("❌ Load state: Failed")
            return False
        
        # Clean up test file
        if os.path.exists('bot_state.json'):
            os.remove('bot_state.json')
            print("✅ Cleanup: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot state test failed: {e}")
        return False

def test_main_components():
    """Test main components can be imported"""
    print("\n🧪 Testing Main Components...")
    print("-" * 50)
    
    components = [
        ('main', 'main.py'),
        ('config', 'config.py'),
        ('database.database', 'database/database.py'),
        ('utils.logger', 'utils/logger.py'),
        ('telegram_control_bot', 'telegram_control_bot.py')
    ]
    
    failed_components = []
    for component, file_path in components:
        try:
            __import__(component)
            print(f"✅ {component}: OK")
        except ImportError as e:
            print(f"❌ {component}: Failed ({e})")
            failed_components.append(component)
        except Exception as e:
            print(f"⚠️ {component}: Warning ({e})")
    
    if failed_components:
        print(f"\n⚠️ Failed components: {', '.join(failed_components)}")
        return False
    else:
        print("\n✅ All main components imported successfully!")
        return True

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Telegram Control Bot Tests...")
    print("=" * 60)
    
    tests = [
        ("Environment Configuration", test_environment),
        ("Module Imports", test_imports),
        ("File Structure", test_file_structure),
        ("Bot State Management", test_bot_state),
        ("Main Components", test_main_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: Exception occurred - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Telegram control bot is ready to use.")
        print("\n🚀 Next steps:")
        print("1. Run: python run_telegram_bot.py")
        print("2. Choose option 1 to start the Telegram control bot")
        print("3. Start a chat with your bot and use /start")
        return True
    else:
        print("⚠️ Some tests failed. Please fix the issues before running the bot.")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
