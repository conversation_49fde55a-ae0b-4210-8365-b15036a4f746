#!/usr/bin/env python3
"""
Football News Bot - Telegram Control Interface Runner
مشغل بوت التحكم في وكيل أخبار كرة القدم

This script runs the Telegram control bot for the football news agent.
"""

import os
import sys
import subprocess
from utils.logger import logger

def print_banner():
    """Print a nice banner"""
    banner = """
    ==========================================
       🤖 Football News Bot - Telegram Control       
    ========================================== 
    """
    print(banner)

def check_requirements():
    """Check if all requirements are installed"""
    try:
        import telegram
        import dotenv
        import requests
        import beautifulsoup4
        import selenium
        import google.auth
        import google.generativeai
        import PIL
        logger.info("All requirements are installed")
        return True
    except ImportError as e:
        logger.error(f"Missing requirement: {e}")
        print(f"❌ Missing requirement: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists and has required tokens"""
    if not os.path.exists('.env'):
        logger.warning(".env file not found")
        print("❌ .env file not found. Please create it from env.example")
        return False

    # Check for required tokens
    from dotenv import load_dotenv
    load_dotenv()

    control_token = os.getenv('TELEGRAM_CONTROL_BOT_TOKEN')

    if not control_token or control_token == 'YOUR_CONTROL_BOT_TOKEN_HERE':
        print("❌ TELEGRAM_CONTROL_BOT_TOKEN not set in .env file")
        print("Please add your Telegram bot token for control")
        return False

    logger.info(".env file found with required tokens")
    print("✅ البوت متاح لجميع المستخدمين")
    return True

def get_bot_setup_help():
    """Show help for setting up Telegram bot"""
    help_text = """
🤖 كيفية إنشاء بوت تيليجرام جديد:

1️⃣ ابدأ محادثة مع: @BotFather
2️⃣ أرسل: /newbot
3️⃣ اختر اسم للبوت (مثل: Football News Control)
4️⃣ اختر username للبوت (يجب أن ينتهي بـ bot)
5️⃣ انسخ الـ Token وضعه في ملف .env

مثال في ملف .env:
TELEGRAM_CONTROL_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

📱 بعد إنشاء البوت:
1️⃣ شغل البوت باستخدام: python run_telegram_bot.py
2️⃣ ابحث عن البوت في التيليجرام باستخدام username
3️⃣ ابدأ محادثة واستخدم /start
4️⃣ استمتع بالتحكم في وكيل الأخبار!

🔓 ملاحظة: البوت متاح لجميع المستخدمين
"""
    print(help_text)

def run_telegram_bot():
    """Run the Telegram control bot"""
    print("🚀 Starting Telegram Control Bot...")
    logger.info("Starting Telegram control bot")
    
    try:
        # Run the telegram control bot
        result = subprocess.run([sys.executable, 'telegram_control_bot.py'], 
                              capture_output=False, text=True)
        if result.returncode != 0:
            print("❌ Telegram bot failed to start!")
            logger.error("Telegram control bot failed to start")
        else:
            print("✅ Telegram bot stopped normally")
            logger.info("Telegram control bot stopped normally")
    except KeyboardInterrupt:
        print("\n⏹️ Telegram bot stopped by user")
        logger.info("Telegram control bot stopped by user")
    except Exception as e:
        print(f"❌ Error running Telegram bot: {e}")
        logger.error(f"Error running Telegram control bot: {e}")

def run_autonomous_mode():
    """Run the bot in autonomous mode (original behavior)"""
    print("🤖 Starting Autonomous Mode...")
    print("The bot will run continuously every 30 minutes")
    print("Press Ctrl+C to stop")
    logger.info("Starting autonomous mode")
    
    try:
        subprocess.run([sys.executable, 'main.py'], capture_output=False, text=True)
    except KeyboardInterrupt:
        print("\n⏹️ Autonomous bot stopped by user")
        logger.info("Autonomous bot stopped by user")
    except Exception as e:
        print(f"❌ Error running autonomous bot: {e}")
        logger.error(f"Error running autonomous bot: {e}")

def run_test_mode():
    """Run a single test cycle"""
    print("🧪 Running Test Mode (single cycle)...")
    logger.info("Starting test mode")
    
    try:
        result = subprocess.run([sys.executable, 'main.py', '--test'], 
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ Test completed successfully!")
            logger.info("Test mode completed successfully")
        else:
            print("❌ Test failed!")
            logger.error("Test mode failed")
    except Exception as e:
        print(f"❌ Error running test: {e}")
        logger.error(f"Error running test: {e}")

def show_status():
    """Show current system status"""
    print("\n📊 System Status:")
    print("-" * 50)
    
    # Check files
    files_to_check = [
        (".env", "Environment file"),
        ("requirements.txt", "Requirements file"),
        ("database/news.db", "Database file"),
        ("main.py", "Main script"),
        ("telegram_control_bot.py", "Telegram control bot"),
    ]
    
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    # Check directories
    dirs_to_check = ["scraper", "generator", "publisher", "utils", "database"]
    for dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}/ directory: Found")
        else:
            print(f"❌ {dir_path}/ directory: Missing")
    
    # Check bot state
    if os.path.exists('bot_state.json'):
        try:
            import json
            with open('bot_state.json', 'r', encoding='utf-8') as f:
                state = json.load(f)
                is_running = state.get('is_running', False)
                articles_count = state.get('articles_processed', 0)
                print(f"🤖 Bot Status: {'Running' if is_running else 'Stopped'}")
                print(f"📰 Articles Processed: {articles_count}")
        except:
            print("⚠️ Bot state file exists but couldn't be read")
    else:
        print("📝 No bot state file found (first run)")

def main():
    """Main function"""
    print_banner()
    
    # Basic checks
    if not check_requirements():
        return
    
    env_ok = check_env_file()
    
    while True:
        print("\n🎯 Choose an option:")
        print("1. 🤖 Run Telegram Control Bot (Recommended)")
        print("2. 🔄 Run Autonomous Mode (Original)")
        print("3. 🧪 Run Test Mode (Single Cycle)")
        print("4. 📊 Show System Status")
        print("5. 📱 Help: Setup Telegram Bot")
        print("6. 🚪 Exit")

        try:
            choice = input("\nEnter your choice (1-6): ").strip()

            if choice == '1':
                if not env_ok:
                    print("❌ Please fix .env file configuration first")
                    continue
                run_telegram_bot()
            elif choice == '2':
                run_autonomous_mode()
            elif choice == '3':
                run_test_mode()
            elif choice == '4':
                show_status()
            elif choice == '5':
                get_bot_setup_help()
            elif choice == '6':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-6.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == '__main__':
    main()
