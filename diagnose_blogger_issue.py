#!/usr/bin/env python3
"""
تشخيص مشكلة Blogger API
يساعد في تحديد السبب الدقيق لمشكلة 403 Forbidden
"""

import json
import sys
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from utils.logger import logger
import config

def diagnose_blogger_issue():
    """تشخيص شامل لمشكلة Blogger"""
    
    print("🔍 تشخيص مشكلة Blogger API")
    print("=" * 50)
    
    # 1. فحص المعلومات الأساسية
    print("1️⃣ فحص المعلومات الأساسية...")
    
    try:
        token_data = json.loads(config.GOOGLE_OAUTH_TOKEN)
        creds = Credentials.from_authorized_user_info(
            token_data, 
            ['https://www.googleapis.com/auth/blogger']
        )
        
        if creds.expired and creds.refresh_token:
            creds.refresh(Request())
        
        service = build('blogger', 'v3', credentials=creds)
        
        # فحص معلومات المدونة
        blog = service.blogs().get(blogId=config.BLOG_ID).execute()
        
        print(f"📝 اسم المدونة: {blog.get('name')}")
        print(f"🔗 رابط المدونة: {blog.get('url')}")
        print(f"📊 عدد المنشورات: {blog.get('posts', {}).get('totalItems', 0)}")
        print(f"📅 تاريخ الإنشاء: {blog.get('published', 'غير معروف')}")
        
        # فحص معلومات المستخدم
        user_info = service.users().get(userId='self').execute()
        print(f"👤 المستخدم الحالي: {user_info.get('displayName', 'غير معروف')}")
        print(f"📧 البريد الإلكتروني: {user_info.get('id', 'غير معروف')}")
        
        # فحص المدونات المتاحة للمستخدم
        print("\n2️⃣ فحص المدونات المتاحة للمستخدم...")
        blogs_list = service.blogs().listByUser(userId='self').execute()
        
        user_blogs = blogs_list.get('items', [])
        print(f"📚 عدد المدونات المتاحة: {len(user_blogs)}")
        
        target_blog_found = False
        for blog_item in user_blogs:
            blog_id = blog_item.get('id')
            blog_name = blog_item.get('name')
            blog_url = blog_item.get('url')
            
            print(f"  📖 {blog_name} (ID: {blog_id})")
            print(f"     🔗 {blog_url}")
            
            if blog_id == config.BLOG_ID:
                target_blog_found = True
                print("     ✅ هذه هي المدونة المستهدفة!")
        
        if not target_blog_found:
            print(f"\n❌ المدونة المستهدفة (ID: {config.BLOG_ID}) غير موجودة في قائمة مدونات المستخدم!")
            print("💡 هذا يعني أن المستخدم المصادق عليه ليس مالك هذه المدونة")
            return False
        
        # 3. فحص صلاحيات محددة
        print("\n3️⃣ فحص الصلاحيات المحددة...")
        
        try:
            # محاولة الحصول على قائمة المنشورات
            posts = service.posts().list(blogId=config.BLOG_ID, maxResults=1).execute()
            print("✅ صلاحية القراءة: متاحة")
            
            # محاولة إنشاء منشور تجريبي
            test_post = {
                "kind": "blogger#post",
                "blog": {"id": config.BLOG_ID},
                "title": "اختبار صلاحيات API - سيتم حذفه",
                "content": "<p>منشور تجريبي لاختبار صلاحيات الكتابة</p>",
                "labels": ["اختبار"]
            }
            
            # محاولة إنشاء draft
            draft = service.posts().insert(
                blogId=config.BLOG_ID, 
                body=test_post, 
                isDraft=True
            ).execute()
            
            print("✅ صلاحية الكتابة: متاحة")
            
            # حذف المنشور التجريبي
            service.posts().delete(blogId=config.BLOG_ID, postId=draft['id']).execute()
            print("✅ صلاحية الحذف: متاحة")
            
            print("\n🎉 جميع الصلاحيات متاحة! المشكلة قد تكون مؤقتة")
            return True
            
        except HttpError as e:
            print(f"❌ خطأ في الصلاحيات: {e}")
            
            if e.resp.status == 403:
                print("\n💡 تحليل خطأ 403:")
                error_details = e.error_details if hasattr(e, 'error_details') else []
                
                for detail in error_details:
                    reason = detail.get('reason', 'unknown')
                    message = detail.get('message', 'No message')
                    
                    print(f"   🔍 السبب: {reason}")
                    print(f"   📝 الرسالة: {message}")
                    
                    if reason == 'forbidden':
                        print("   💡 الحل المقترح: تحقق من أن المستخدم مالك المدونة")
                    elif reason == 'quotaExceeded':
                        print("   💡 الحل المقترح: انتظر قليلاً ثم حاول مرة أخرى")
                    elif reason == 'rateLimitExceeded':
                        print("   💡 الحل المقترح: قلل من عدد الطلبات")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def suggest_solutions():
    """اقتراح حلول للمشكلة"""
    
    print("\n💡 الحلول المقترحة:")
    print("=" * 30)
    
    print("1️⃣ تحقق من ملكية المدونة:")
    print("   - اذهب إلى https://www.blogger.com")
    print("   - تأكد من أنك مسجل دخول بنفس الحساب المستخدم في API")
    print("   - تحقق من أن المدونة تظهر في قائمة مدوناتك")
    
    print("\n2️⃣ تحقق من إعدادات المدونة:")
    print("   - اذهب إلى إعدادات المدونة")
    print("   - تحقق من إعدادات الخصوصية")
    print("   - تأكد من أن المدونة ليست محظورة أو مقيدة")
    
    print("\n3️⃣ تحقق من Google Cloud Console:")
    print("   - اذهب إلى https://console.cloud.google.com")
    print("   - تأكد من تفعيل Blogger API")
    print("   - تحقق من حدود الاستخدام (Quotas)")
    print("   - تأكد من صحة OAuth 2.0 credentials")
    
    print("\n4️⃣ جرب إنشاء مدونة جديدة:")
    print("   - أنشئ مدونة جديدة على Blogger")
    print("   - احصل على Blog ID الجديد")
    print("   - حدث ملف .env بالـ Blog ID الجديد")
    
    print("\n5️⃣ تحقق من صلاحيات OAuth:")
    print("   - تأكد من أن التطبيق يطلب صلاحية 'https://www.googleapis.com/auth/blogger'")
    print("   - جرب إعادة المصادقة مع صلاحيات أوسع")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 أداة تشخيص مشكلة Blogger API")
    
    success = diagnose_blogger_issue()
    
    if not success:
        suggest_solutions()
        
        print("\n❓ هل تريد محاولة إنشاء مدونة جديدة؟ (y/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['y', 'yes', 'نعم', 'ن']:
                print("\n📝 خطوات إنشاء مدونة جديدة:")
                print("1. اذهب إلى https://www.blogger.com")
                print("2. انقر على 'إنشاء مدونة جديدة'")
                print("3. اختر اسماً ورابطاً للمدونة")
                print("4. بعد الإنشاء، انسخ Blog ID من الرابط")
                print("5. حدث ملف .env بالـ Blog ID الجديد")
        except KeyboardInterrupt:
            print("\n👋 تم الإلغاء")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
