# 🤖 دليل بوت التيليجرام لوكيل أخبار كرة القدم

## 📋 نظرة عامة

تم تحويل وكيل أخبار كرة القدم إلى نظام هجين يجمع بين:
- **العمل التلقائي المستقل** (كما كان سابقاً)
- **التحكم الكامل عبر بوت التيليجرام**

## 🚀 المميزات الجديدة

### ✅ التحكم الكامل عبر التيليجرام
- تشغيل وإيقاف الوكيل عن بُعد
- مراقبة الحالة والإحصائيات
- عرض آخر الأنشطة
- تحديثات فورية

### ✅ العمل المستقل
- يعمل تلقائياً كل 30 دقيقة
- لا يحتاج تدخل بشري
- يحفظ حالته ويستكملها عند إعادة التشغيل

## 🛠️ الإعداد

### 1. إنشاء بوت تيليجرام جديد

1. ابدأ محادثة مع [@BotFather](https://t.me/BotFather)
2. أرسل `/newbot`
3. اختر اسم للبوت (مثل: "Football News Control Bot")
4. اختر username للبوت (يجب أن ينتهي بـ `bot`)
5. انسخ الـ Token الذي سيعطيه لك

### 2. الحصول على User ID الخاص بك

1. ابدأ محادثة مع [@userinfobot](https://t.me/userinfobot)
2. أرسل أي رسالة
3. انسخ رقم الـ User ID

### 3. تحديث ملف .env

أضف هذين السطرين في ملف `.env`:

```env
# Telegram Bot Token for Control
TELEGRAM_CONTROL_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

# Telegram Admin User ID
TELEGRAM_ADMIN_USER_ID=123456789
```

## 🎯 طرق التشغيل

### الطريقة الأولى: بوت التيليجرام (موصى بها)

```bash
python run_telegram_bot.py
```

أو اختر الخيار 1 من القائمة

### الطريقة الثانية: الوضع التلقائي (الأصلي)

```bash
python main.py
```

أو اختر الخيار 2 من القائمة

### الطريقة الثالثة: وضع الاختبار

```bash
python main.py --test
```

أو اختر الخيار 3 من القائمة

## 📱 استخدام بوت التيليجرام

### الأوامر المتاحة

- `/start` - عرض لوحة التحكم الرئيسية

### الأزرار التفاعلية

- **📊 حالة البوت** - عرض الحالة الحالية
- **▶️ تشغيل البوت** - بدء تشغيل الوكيل
- **⏹️ إيقاف البوت** - إيقاف الوكيل
- **📈 إحصائيات** - عرض إحصائيات مفصلة
- **🔄 تحديث** - تحديث المعلومات

### معلومات الحالة

يعرض البوت:
- حالة التشغيل (يعمل/متوقف)
- وقت التشغيل
- عدد المقالات المنشورة
- وقت آخر مقال
- موعد الدورة التالية

## 🔧 الملفات الجديدة

### `telegram_control_bot.py`
البوت الرئيسي للتحكم عبر التيليجرام

### `run_telegram_bot.py`
مشغل البوت مع واجهة سهلة الاستخدام

### `bot_state.json`
ملف حفظ حالة البوت (ينشأ تلقائياً)

## 🛡️ الأمان

- فقط المستخدم المحدد في `TELEGRAM_ADMIN_USER_ID` يمكنه التحكم
- إذا لم تحدد User ID، سيحذرك النظام
- جميع العمليات مسجلة في ملفات السجل

## 🔄 كيفية العمل

### الوضع التلقائي
1. يبدأ الوكيل تلقائياً
2. يجلب الأخبار من المصادر
3. ينشئ المحتوى باستخدام AI
4. ينشر على Blogger
5. يرسل إشعار تيليجرام
6. ينتظر 30 دقيقة ويكرر

### الوضع المتحكم به
1. تشغل بوت التيليجرام
2. تستخدم الأوامر للتحكم
3. الوكيل يعمل في الخلفية
4. تراقب التقدم عبر البوت

## 🚨 استكشاف الأخطاء

### البوت لا يستجيب
- تأكد من صحة `TELEGRAM_CONTROL_BOT_TOKEN`
- تأكد من أن البوت مفعل مع BotFather

### "غير مصرح لك"
- تأكد من صحة `TELEGRAM_ADMIN_USER_ID`
- تأكد من استخدام User ID وليس Username

### خطأ في التشغيل
- تأكد من تثبيت جميع المتطلبات: `pip install -r requirements.txt`
- تأكد من وجود ملف `.env` مع جميع المفاتيح

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملفات السجل
2. استخدم وضع الاختبار للتشخيص
3. تأكد من صحة جميع المفاتيح في `.env`

## 🎉 الخلاصة

الآن لديك:
- ✅ وكيل أخبار يعمل تلقائياً
- ✅ تحكم كامل عبر التيليجرام
- ✅ مراقبة مستمرة للحالة
- ✅ نظام آمن ومحمي
- ✅ سهولة في الاستخدام

استمتع بوكيل الأخبار الجديد! 🚀
