#!/usr/bin/env python3
"""
Football News Bot - Telegram Control Interface
بوت التحكم في وكيل أخبار كرة القدم عبر التيليجرام

This bot allows you to control the football news agent via Telegram commands.
"""

import os
import asyncio
import threading
import time
import json
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
from dotenv import load_dotenv
from utils.logger import logger

# Load environment variables
load_dotenv()

# Bot configuration
CONTROL_BOT_TOKEN = os.getenv('TELEGRAM_CONTROL_BOT_TOKEN')
ADMIN_USER_ID = os.getenv('TELEGRAM_ADMIN_USER_ID')

# Bot state management
class BotState:
    def __init__(self):
        self.is_running = False
        self.main_thread = None
        self.start_time = None
        self.articles_processed = 0
        self.last_article_time = None
        
    def save_state(self):
        """Save bot state to file"""
        state_data = {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'articles_processed': self.articles_processed,
            'last_article_time': self.last_article_time.isoformat() if self.last_article_time else None
        }
        with open('bot_state.json', 'w', encoding='utf-8') as f:
            json.dump(state_data, f, ensure_ascii=False, indent=2)
    
    def load_state(self):
        """Load bot state from file"""
        try:
            if os.path.exists('bot_state.json'):
                with open('bot_state.json', 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                    self.is_running = state_data.get('is_running', False)
                    self.articles_processed = state_data.get('articles_processed', 0)
                    if state_data.get('start_time'):
                        self.start_time = datetime.fromisoformat(state_data['start_time'])
                    if state_data.get('last_article_time'):
                        self.last_article_time = datetime.fromisoformat(state_data['last_article_time'])
        except Exception as e:
            logger.error(f"Error loading bot state: {e}")

# Global bot state
bot_state = BotState()

def check_admin(func):
    """Decorator to check if user is admin"""
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        user_id = str(update.effective_user.id)
        if ADMIN_USER_ID and user_id != ADMIN_USER_ID:
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return
        return await func(update, context)
    return wrapper

def run_main_bot():
    """Run the main football news bot"""
    try:
        from main import main_cycle
        from database import database
        
        # Initialize database
        database.init_db()
        logger.info("Database initialized for bot run")
        
        bot_state.start_time = datetime.now()
        bot_state.is_running = True
        bot_state.save_state()
        
        while bot_state.is_running:
            try:
                logger.info("Starting main cycle from Telegram bot control")
                main_cycle()
                bot_state.articles_processed += 1
                bot_state.last_article_time = datetime.now()
                bot_state.save_state()
                
                # Wait for 30 minutes (1800 seconds) before next cycle
                for i in range(1800):
                    if not bot_state.is_running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"Error in main cycle: {e}")
                # Wait 5 minutes before retrying
                for i in range(300):
                    if not bot_state.is_running:
                        break
                    time.sleep(1)
                    
    except Exception as e:
        logger.error(f"Critical error in main bot: {e}")
    finally:
        bot_state.is_running = False
        bot_state.save_state()

@check_admin
async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command"""
    keyboard = [
        [InlineKeyboardButton("📊 حالة البوت", callback_data="status")],
        [InlineKeyboardButton("▶️ تشغيل البوت", callback_data="start_bot"),
         InlineKeyboardButton("⏹️ إيقاف البوت", callback_data="stop_bot")],
        [InlineKeyboardButton("📈 إحصائيات", callback_data="stats"),
         InlineKeyboardButton("🔄 تحديث", callback_data="refresh")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    welcome_text = """
🤖 **مرحباً بك في لوحة تحكم وكيل أخبار كرة القدم**

هذا البوت يتيح لك التحكم الكامل في وكيل الأخبار:

🔹 **تشغيل/إيقاف** الوكيل
🔹 **مراقبة الحالة** والإحصائيات  
🔹 **عرض آخر الأنشطة**

الوكيل يعمل بشكل مستقل كل 30 دقيقة لجلب ونشر أخبار كرة القدم الجديدة.

استخدم الأزرار أدناه للتحكم:
"""
    
    await update.message.reply_text(welcome_text, reply_markup=reply_markup, parse_mode='Markdown')

@check_admin
async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button callbacks"""
    query = update.callback_query
    await query.answer()
    
    if query.data == "status":
        await show_status(query)
    elif query.data == "start_bot":
        await start_bot(query)
    elif query.data == "stop_bot":
        await stop_bot(query)
    elif query.data == "stats":
        await show_stats(query)
    elif query.data == "refresh":
        await refresh_status(query)

async def show_status(query):
    """Show current bot status"""
    bot_state.load_state()
    
    if bot_state.is_running:
        status_emoji = "🟢"
        status_text = "يعمل"
        uptime = datetime.now() - bot_state.start_time if bot_state.start_time else None
        uptime_text = f"منذ {uptime.total_seconds()//3600:.0f} ساعة و {(uptime.total_seconds()%3600)//60:.0f} دقيقة" if uptime else "غير محدد"
    else:
        status_emoji = "🔴"
        status_text = "متوقف"
        uptime_text = "غير نشط"
    
    last_article = "لم يتم نشر مقالات بعد"
    if bot_state.last_article_time:
        time_diff = datetime.now() - bot_state.last_article_time
        if time_diff.total_seconds() < 3600:
            last_article = f"منذ {time_diff.total_seconds()//60:.0f} دقيقة"
        else:
            last_article = f"منذ {time_diff.total_seconds()//3600:.0f} ساعة"
    
    status_message = f"""
📊 **حالة وكيل أخبار كرة القدم**

{status_emoji} **الحالة:** {status_text}
⏰ **وقت التشغيل:** {uptime_text}
📰 **المقالات المنشورة:** {bot_state.articles_processed}
🕐 **آخر مقال:** {last_article}

**الدورة التالية:** خلال 30 دقيقة من آخر دورة
"""
    
    keyboard = [
        [InlineKeyboardButton("🔄 تحديث", callback_data="refresh")],
        [InlineKeyboardButton("▶️ تشغيل", callback_data="start_bot") if not bot_state.is_running 
         else InlineKeyboardButton("⏹️ إيقاف", callback_data="stop_bot")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await query.edit_message_text(status_message, reply_markup=reply_markup, parse_mode='Markdown')

async def start_bot(query):
    """Start the main bot"""
    if bot_state.is_running:
        await query.edit_message_text("⚠️ البوت يعمل بالفعل!")
        return
    
    try:
        # Start the main bot in a separate thread
        bot_state.main_thread = threading.Thread(target=run_main_bot, daemon=True)
        bot_state.main_thread.start()
        
        await query.edit_message_text("✅ تم تشغيل وكيل الأخبار بنجاح!\n\n🔄 سيبدأ العمل خلال ثوانٍ...")
        
        # Wait a moment then show status
        await asyncio.sleep(2)
        await show_status(query)
        
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        await query.edit_message_text(f"❌ خطأ في تشغيل البوت: {str(e)}")

async def stop_bot(query):
    """Stop the main bot"""
    if not bot_state.is_running:
        await query.edit_message_text("⚠️ البوت متوقف بالفعل!")
        return
    
    try:
        bot_state.is_running = False
        bot_state.save_state()
        
        await query.edit_message_text("✅ تم إيقاف وكيل الأخبار بنجاح!")
        
        # Wait a moment then show status
        await asyncio.sleep(2)
        await show_status(query)
        
    except Exception as e:
        logger.error(f"Error stopping bot: {e}")
        await query.edit_message_text(f"❌ خطأ في إيقاف البوت: {str(e)}")

async def show_stats(query):
    """Show detailed statistics"""
    bot_state.load_state()
    
    # Try to get database stats
    try:
        from database import database
        # Get total articles from database
        # This would need to be implemented in database.py
        total_articles = "غير متوفر"
    except:
        total_articles = "غير متوفر"
    
    stats_message = f"""
📈 **إحصائيات مفصلة**

📊 **إحصائيات الجلسة الحالية:**
• المقالات المنشورة: {bot_state.articles_processed}
• وقت البدء: {bot_state.start_time.strftime('%Y-%m-%d %H:%M') if bot_state.start_time else 'غير محدد'}
• آخر نشاط: {bot_state.last_article_time.strftime('%Y-%m-%d %H:%M') if bot_state.last_article_time else 'لا يوجد'}

📚 **إحصائيات عامة:**
• إجمالي المقالات: {total_articles}
• حالة قاعدة البيانات: متصلة ✅

🔄 **معلومات النظام:**
• دورة كل: 30 دقيقة
• مصادر الأخبار: Sky News + Kooora
• النشر على: Blogger + إشعارات Telegram
"""
    
    keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="status")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await query.edit_message_text(stats_message, reply_markup=reply_markup, parse_mode='Markdown')

async def refresh_status(query):
    """Refresh and show current status"""
    await show_status(query)

def main():
    """Main function to run the control bot"""
    if not CONTROL_BOT_TOKEN:
        print("❌ خطأ: TELEGRAM_CONTROL_BOT_TOKEN غير موجود في ملف .env")
        print("يرجى إضافة توكن البوت في ملف .env")
        return
    
    if not ADMIN_USER_ID:
        print("⚠️ تحذير: TELEGRAM_ADMIN_USER_ID غير موجود في ملف .env")
        print("سيتمكن أي شخص من استخدام البوت!")
    
    # Load saved state
    bot_state.load_state()
    
    # Create application
    application = Application.builder().token(CONTROL_BOT_TOKEN).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CallbackQueryHandler(button_callback))
    
    print("🤖 بوت التحكم في وكيل الأخبار جاهز!")
    print("📱 ابدأ محادثة مع البوت واستخدم /start")
    
    # Run the bot
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
