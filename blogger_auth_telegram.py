#!/usr/bin/env python3
"""
Blogger Authentication via Telegram
مصادقة بلوجر عبر التيليجرام

This module handles Blogger OAuth authentication through Telegram bot interface.
"""

import os
import json
import asyncio
import webbrowser
import tempfile
from datetime import datetime, timedelta
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from utils.logger import logger

# Scopes required for Blogger API
SCOPES = ['https://www.googleapis.com/auth/blogger']

class BloggerAuthTelegram:
    """Manages Blogger OAuth authentication via Telegram"""
    
    def __init__(self):
        self.credentials = None
        self.service = None
        self.auth_flow = None
        self.auth_url = None
        
    def create_oauth_config(self, client_id, client_secret):
        """Create OAuth configuration from provided credentials"""
        config = {
            "installed": {
                "client_id": client_id,
                "client_secret": client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "redirect_uris": ["http://localhost"]
            }
        }
        return config
    
    async def start_auth_flow(self, client_id, client_secret):
        """Start OAuth authentication flow"""
        try:
            # Create temporary config file
            oauth_config = self.create_oauth_config(client_id, client_secret)
            
            # Create flow from config
            self.auth_flow = InstalledAppFlow.from_client_config(
                oauth_config, SCOPES)
            
            # Generate authorization URL
            self.auth_url, _ = self.auth_flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true'
            )
            
            logger.info("✅ OAuth flow started successfully")
            return True, self.auth_url
            
        except Exception as e:
            logger.error(f"❌ Failed to start OAuth flow: {e}")
            return False, str(e)
    
    async def complete_auth_flow(self, authorization_code):
        """Complete OAuth authentication with authorization code"""
        try:
            if not self.auth_flow:
                return False, "Authentication flow not started"
            
            # Exchange authorization code for credentials
            self.auth_flow.fetch_token(code=authorization_code)
            self.credentials = self.auth_flow.credentials
            
            # Save credentials to .env
            await self.save_credentials_to_env()
            
            logger.info("✅ OAuth authentication completed successfully")
            return True, "Authentication successful"
            
        except Exception as e:
            logger.error(f"❌ Failed to complete OAuth flow: {e}")
            return False, str(e)
    
    async def save_credentials_to_env(self):
        """Save credentials to .env file"""
        try:
            if not self.credentials:
                return False
            
            # Convert credentials to dict
            token_data = {
                'token': self.credentials.token,
                'refresh_token': self.credentials.refresh_token,
                'token_uri': self.credentials.token_uri,
                'client_id': self.credentials.client_id,
                'client_secret': self.credentials.client_secret,
                'scopes': self.credentials.scopes,
                'expiry': self.credentials.expiry.isoformat() if self.credentials.expiry else None
            }
            
            token_json = json.dumps(token_data)
            
            # Read current .env file
            env_lines = []
            if os.path.exists('.env'):
                with open('.env', 'r', encoding='utf-8') as f:
                    env_lines = f.readlines()
            
            # Update or add GOOGLE_OAUTH_TOKEN
            token_updated = False
            for i, line in enumerate(env_lines):
                if line.startswith('GOOGLE_OAUTH_TOKEN=') or line.startswith('# Blogger OAuth credentials'):
                    env_lines[i] = f'GOOGLE_OAUTH_TOKEN={token_json}\n'
                    token_updated = True
                    break
            
            if not token_updated:
                env_lines.append(f'GOOGLE_OAUTH_TOKEN={token_json}\n')
            
            # Write back to .env file
            with open('.env', 'w', encoding='utf-8') as f:
                f.writelines(env_lines)
            
            logger.info("✅ Credentials saved to .env file")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save credentials: {e}")
            return False
    
    async def load_credentials_from_env(self):
        """Load credentials from environment variable"""
        try:
            from config import GOOGLE_OAUTH_TOKEN
            
            if not GOOGLE_OAUTH_TOKEN:
                return None
            
            # Handle both string and already parsed dict
            if isinstance(GOOGLE_OAUTH_TOKEN, str):
                token_data = json.loads(GOOGLE_OAUTH_TOKEN)
            else:
                token_data = GOOGLE_OAUTH_TOKEN
            
            creds = Credentials.from_authorized_user_info(token_data, SCOPES)
            
            if creds and creds.valid:
                logger.info("✅ Valid credentials loaded from environment")
                self.credentials = creds
                return creds
            elif creds and creds.expired and creds.refresh_token:
                logger.info("🔄 Credentials expired, attempting to refresh...")
                try:
                    creds.refresh(Request())
                    logger.info("✅ Credentials refreshed successfully")
                    
                    # Update environment variable with new token
                    await self.save_credentials_to_env()
                    self.credentials = creds
                    return creds
                except Exception as e:
                    logger.error(f"❌ Failed to refresh credentials: {e}")
                    return None
            else:
                logger.warning("⚠️ Invalid credentials in environment")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error loading credentials: {e}")
            return None
    
    async def test_blogger_connection(self, blog_id):
        """Test connection to Blogger API"""
        try:
            if not self.credentials:
                creds = await self.load_credentials_from_env()
                if not creds:
                    return False, "No valid credentials available"
                self.credentials = creds
            
            # Build Blogger service
            service = build('blogger', 'v3', credentials=self.credentials)
            
            # Test by getting blog info
            blog = service.blogs().get(blogId=blog_id).execute()
            blog_name = blog.get('name', 'Unknown')
            blog_url = blog.get('url', 'Unknown')
            
            logger.info(f"✅ Successfully connected to blog: {blog_name}")
            return True, f"Connected to blog: {blog_name}\nURL: {blog_url}"
            
        except Exception as e:
            logger.error(f"❌ Blogger connection test failed: {e}")
            return False, str(e)
    
    async def delete_credentials(self):
        """Delete credentials from .env file"""
        try:
            env_lines = []
            if os.path.exists('.env'):
                with open('.env', 'r', encoding='utf-8') as f:
                    env_lines = f.readlines()
                
                # Filter out the token line and replace with comment
                new_env_lines = []
                for line in env_lines:
                    if line.startswith('GOOGLE_OAUTH_TOKEN='):
                        new_env_lines.append('# Blogger OAuth credentials will be managed via Telegram bot\n')
                        new_env_lines.append('# GOOGLE_OAUTH_TOKEN will be set automatically through the bot\n')
                    else:
                        new_env_lines.append(line)
                
                with open('.env', 'w', encoding='utf-8') as f:
                    f.writelines(new_env_lines)
            
            # Reset internal state
            self.credentials = None
            self.service = None
            
            logger.info("✅ Credentials deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete credentials: {e}")
            return False

# Global instance
blogger_auth = BloggerAuthTelegram()

async def handle_blogger_auth_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle start of Blogger authentication"""
    keyboard = [
        [InlineKeyboardButton("🔐 بدء المصادقة", callback_data="auth_start")],
        [InlineKeyboardButton("🧪 اختبار الاتصال", callback_data="auth_test")],
        [InlineKeyboardButton("🗑️ حذف بيانات المصادقة", callback_data="auth_delete")],
        [InlineKeyboardButton("🔙 العودة", callback_data="status")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    auth_text = """
🔐 **إدارة مصادقة Blogger**

اختر العملية المطلوبة:

🔹 **بدء المصادقة** - إعداد مصادقة جديدة
🔹 **اختبار الاتصال** - فحص المصادقة الحالية  
🔹 **حذف بيانات المصادقة** - إزالة البيانات الحالية

**ملاحظة:** ستحتاج إلى:
• Client ID من Google Cloud Console
• Client Secret من Google Cloud Console
• Blog ID من Blogger
"""
    
    if update.callback_query:
        await update.callback_query.edit_message_text(auth_text, reply_markup=reply_markup, parse_mode='Markdown')
    else:
        await update.message.reply_text(auth_text, reply_markup=reply_markup, parse_mode='Markdown')

async def handle_auth_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle authentication callbacks"""
    query = update.callback_query
    await query.answer()
    
    if query.data == "auth_start":
        await start_auth_process(query, context)
    elif query.data == "auth_test":
        await test_auth_connection(query, context)
    elif query.data == "auth_delete":
        await delete_auth_data(query, context)

async def start_auth_process(query, context):
    """Start the authentication process"""
    text = """
🔐 **بدء عملية المصادقة**

يرجى إرسال بيانات Google OAuth بالتنسيق التالي:

```
CLIENT_ID:your_client_id_here
CLIENT_SECRET:your_client_secret_here
BLOG_ID:your_blog_id_here
```

**مثال:**
```
CLIENT_ID:123456789-abc.apps.googleusercontent.com
CLIENT_SECRET:GOCSPX-abcdefghijklmnop
BLOG_ID:1234567890123456789
```

**كيفية الحصول على هذه البيانات:**
1. اذهب إلى Google Cloud Console
2. أنشئ مشروع جديد أو اختر موجود
3. فعل Blogger API
4. أنشئ OAuth 2.0 credentials
5. احصل على Blog ID من Blogger
"""
    
    keyboard = [[InlineKeyboardButton("🔙 إلغاء", callback_data="blogger_auth")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')
    
    # Set state for next message
    context.user_data['waiting_for_oauth_data'] = True

async def test_auth_connection(query, context):
    """Test current authentication"""
    await query.edit_message_text("🧪 جاري اختبار الاتصال...")
    
    try:
        from config import BLOG_ID
        
        if not BLOG_ID:
            await query.edit_message_text("❌ BLOG_ID غير موجود في الإعدادات")
            return
        
        success, message = await blogger_auth.test_blogger_connection(BLOG_ID)
        
        if success:
            result_text = f"✅ **اختبار الاتصال نجح**\n\n{message}"
        else:
            result_text = f"❌ **اختبار الاتصال فشل**\n\n{message}"
        
        keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="blogger_auth")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(result_text, reply_markup=reply_markup, parse_mode='Markdown')
        
    except Exception as e:
        await query.edit_message_text(f"❌ خطأ في الاختبار: {str(e)}")

async def delete_auth_data(query, context):
    """Delete authentication data"""
    keyboard = [
        [InlineKeyboardButton("✅ نعم، احذف", callback_data="auth_delete_confirm")],
        [InlineKeyboardButton("❌ إلغاء", callback_data="blogger_auth")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    text = """
⚠️ **تأكيد حذف بيانات المصادقة**

هل أنت متأكد من حذف جميع بيانات مصادقة Blogger؟

سيتم حذف:
• Google OAuth Token
• بيانات الاتصال المحفوظة

ستحتاج لإعادة المصادقة لاحقاً.
"""
    
    await query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')

async def confirm_delete_auth(query, context):
    """Confirm deletion of auth data"""
    await query.edit_message_text("🗑️ جاري حذف بيانات المصادقة...")
    
    success = await blogger_auth.delete_credentials()
    
    if success:
        text = "✅ تم حذف بيانات المصادقة بنجاح"
    else:
        text = "❌ فشل في حذف بيانات المصادقة"
    
    keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="blogger_auth")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')

async def handle_oauth_data_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle OAuth data input from user"""
    if not context.user_data.get('waiting_for_oauth_data'):
        return
    
    try:
        message_text = update.message.text.strip()
        
        # Parse the OAuth data
        lines = message_text.split('\n')
        oauth_data = {}
        
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                oauth_data[key.strip().upper()] = value.strip()
        
        # Validate required fields
        required_fields = ['CLIENT_ID', 'CLIENT_SECRET', 'BLOG_ID']
        missing_fields = [field for field in required_fields if field not in oauth_data]
        
        if missing_fields:
            await update.message.reply_text(f"❌ البيانات ناقصة: {', '.join(missing_fields)}")
            return
        
        # Start OAuth flow
        await update.message.reply_text("🔐 جاري بدء عملية المصادقة...")
        
        success, auth_url = await blogger_auth.start_auth_flow(
            oauth_data['CLIENT_ID'], 
            oauth_data['CLIENT_SECRET']
        )
        
        if success:
            # Save BLOG_ID to .env
            await save_blog_id_to_env(oauth_data['BLOG_ID'])
            
            keyboard = [[InlineKeyboardButton("🔗 فتح رابط المصادقة", url=auth_url)]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            auth_text = f"""
✅ **تم إنشاء رابط المصادقة**

1. اضغط على الزر أدناه لفتح رابط المصادقة
2. سجل دخولك بحساب Google
3. اقبل الصلاحيات المطلوبة
4. انسخ الكود الذي سيظهر
5. أرسل الكود هنا

**رابط المصادقة:**
`{auth_url}`
"""
            
            await update.message.reply_text(auth_text, reply_markup=reply_markup, parse_mode='Markdown')
            
            # Set state for authorization code
            context.user_data['waiting_for_oauth_data'] = False
            context.user_data['waiting_for_auth_code'] = True
            
        else:
            await update.message.reply_text(f"❌ فشل في بدء المصادقة: {auth_url}")
            
    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في معالجة البيانات: {str(e)}")
        
    finally:
        context.user_data['waiting_for_oauth_data'] = False

async def handle_auth_code_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle authorization code from user"""
    if not context.user_data.get('waiting_for_auth_code'):
        return
    
    try:
        auth_code = update.message.text.strip()
        
        await update.message.reply_text("🔐 جاري إتمام المصادقة...")
        
        success, message = await blogger_auth.complete_auth_flow(auth_code)
        
        if success:
            await update.message.reply_text("✅ تمت المصادقة بنجاح! يمكنك الآن استخدام النظام.")
        else:
            await update.message.reply_text(f"❌ فشلت المصادقة: {message}")
            
    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في المصادقة: {str(e)}")
        
    finally:
        context.user_data['waiting_for_auth_code'] = False

async def save_blog_id_to_env(blog_id):
    """Save Blog ID to .env file"""
    try:
        env_lines = []
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                env_lines = f.readlines()
        
        # Update or add BLOG_ID
        blog_id_updated = False
        for i, line in enumerate(env_lines):
            if line.startswith('BLOG_ID='):
                env_lines[i] = f'BLOG_ID={blog_id}\n'
                blog_id_updated = True
                break
        
        if not blog_id_updated:
            env_lines.append(f'BLOG_ID={blog_id}\n')
        
        # Write back to .env file
        with open('.env', 'w', encoding='utf-8') as f:
            f.writelines(env_lines)
        
        logger.info(f"✅ Blog ID saved to .env: {blog_id}")
        
    except Exception as e:
        logger.error(f"❌ Failed to save Blog ID: {e}")
